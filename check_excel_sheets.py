import pandas as pd

# 读取Excel文件并检查工作表
xl = pd.ExcelFile('/Users/<USER>/Desktop/12-new/DR项目结构-0705.xlsx')

print('所有工作表名称:')
for i, sheet_name in enumerate(xl.sheet_names):
    print(f'{i+1}. {sheet_name}')

print('\n检查是否存在方向和体位工作表:')
print('方向工作表存在:', '方向' in xl.sheet_names)
print('体位工作表存在:', '体位' in xl.sheet_names)

# 如果存在这些工作表，显示其内容
if '方向' in xl.sheet_names:
    print('\n=== 方向工作表内容 ===')
    df_direction = pd.read_excel('/Users/<USER>/Desktop/12-new/DR项目结构-0705.xlsx', sheet_name='方向')
    print(f'行数: {len(df_direction)}, 列数: {len(df_direction.columns)}')
    print('列名:', list(df_direction.columns))
    print('前5行数据:')
    print(df_direction.head())

if '体位' in xl.sheet_names:
    print('\n=== 体位工作表内容 ===')
    df_position = pd.read_excel('/Users/<USER>/Desktop/12-new/DR项目结构-0705.xlsx', sheet_name='体位')
    print(f'行数: {len(df_position)}, 列数: {len(df_position.columns)}')
    print('列名:', list(df_position.columns))
    print('前5行数据:')
    print(df_position.head())

print('\n检查完成！')