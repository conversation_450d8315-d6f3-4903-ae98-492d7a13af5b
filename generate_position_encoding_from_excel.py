#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从Excel文件中读取方向和体位编码，生成摆位编码表
包含：二级部位、三级部位、检查项目、摆位、摆位编码
"""

import pandas as pd
import json
from pathlib import Path

def read_excel_sheets(file_path):
    """读取Excel文件的所有sheet"""
    try:
        # 读取所有sheet
        excel_file = pd.ExcelFile(file_path)
        print(f"Excel文件包含的sheet: {excel_file.sheet_names}")
        
        sheets = {}
        for sheet_name in excel_file.sheet_names:
            sheets[sheet_name] = pd.read_excel(file_path, sheet_name=sheet_name)
            print(f"Sheet '{sheet_name}': {sheets[sheet_name].shape[0]} 行, {sheets[sheet_name].shape[1]} 列")
        
        return sheets
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return None

def get_predefined_encodings():
    """获取预定义的体位和方向编码"""
    # 体位编码字典
    body_position_mapping = {
        '仰卧': 'SUP',
        '俯卧': 'PRO', 
        '侧卧': 'LAT',
        '立位': 'STR',
        '站立': 'STR',
        '坐位': 'SIT',
        '蹲位': 'SQU',
        '跪位': 'KNE',
        '外展': 'ABD',
        '内收': 'ADD',
        '屈曲': 'FLX',
        '伸展': 'EXT',
        '旋转': 'ROT',
        '倒立': 'INV',
        '张口': 'OPE',
        '闭口': 'CLO',
        '应力': 'STR',
        '弯曲': 'BEN'
    }
    
    # 方向编码字典
    direction_mapping = {
        '前后': 'AP',
        '后前': 'PA', 
        '左右': 'LAT',
        '右左': 'RL',
        '侧位': 'LAT',
        '正位': 'STD',
        '斜位': 'OBL',
        '轴位': 'AXI',
        '切线': 'TAN',
        '头尾': 'CC',
        '尾头': 'MLO',
        '内外': 'IO',
        '外内': 'OI',
        '左前斜': 'LAO',
        '右前斜': 'RAO',
        '左后斜': 'LPO',
        '右后斜': 'RPO',
        '左侧': 'LL',
        '右侧': 'RL',
        '穿胸': 'TRA',
        '轴斜': 'OBL',
        '内斜': 'INT',
        '外斜': 'EXT',
        '导管': 'DUC',
        '断层': 'TOM'
    }
    
    return body_position_mapping, direction_mapping

def analyze_position_encoding(position_str, body_position_mapping, direction_mapping):
    """分析摆位字符串，提取体位和方向编码"""
    position_str = str(position_str).strip()
    
    # 查找体位编码（优先匹配更长的关键词）
    body_position_code = 'UNK'
    matched_body_keywords = []
    for keyword, code in sorted(body_position_mapping.items(), key=len, reverse=True):
        if keyword in position_str:
            body_position_code = code
            matched_body_keywords.append(keyword)
            break
    
    # 查找方向编码（优先匹配更长的关键词）
    direction_code = 'UNK'
    matched_direction_keywords = []
    for keyword, code in sorted(direction_mapping.items(), key=len, reverse=True):
        if keyword in position_str:
            direction_code = code
            matched_direction_keywords.append(keyword)
            break
    
    return body_position_code, direction_code, matched_body_keywords, matched_direction_keywords

def generate_position_encoding(main_data, body_position_mapping, direction_mapping):
    """生成摆位编码"""
    results = []
    encoding_stats = {'body_position': {}, 'direction': {}}
    
    # 获取摆位列表
    if '摆位' in main_data.columns:
        # 过滤掉'没有'的摆位
        valid_positions = main_data[main_data['摆位'].notna() & (main_data['摆位'] != '没有')]
        positions = valid_positions['摆位'].unique()
        print(f"\n找到 {len(positions)} 个有效摆位（排除'没有'）")
        
        for position in positions:
            position_str = str(position).strip()
            
            # 分析摆位编码
            body_code, direction_code, body_keywords, direction_keywords = analyze_position_encoding(
                position_str, body_position_mapping, direction_mapping
            )
            
            # 统计编码使用情况
            encoding_stats['body_position'][body_code] = encoding_stats['body_position'].get(body_code, 0) + 1
            encoding_stats['direction'][direction_code] = encoding_stats['direction'].get(direction_code, 0) + 1
            
            # 生成完整编码
            full_encoding = f"{body_code}_{direction_code}"
            
            # 查找对应的部位信息
            position_rows = main_data[main_data['摆位'] == position]
            
            for _, row in position_rows.iterrows():
                result = {
                    '二级部位': row.get('二级部位', ''),
                    '三级部位': row.get('三级部位', ''),
                    '检查项目': row.get('名称', ''),
                    '摆位': position_str,
                    '体位编码': body_code,
                    '方向编码': direction_code,
                    '摆位编码': full_encoding,
                    '匹配的体位关键词': ', '.join(body_keywords) if body_keywords else '',
                    '匹配的方向关键词': ', '.join(direction_keywords) if direction_keywords else ''
                }
                results.append(result)
    
    return results, encoding_stats

def main():
    # 文件路径
    excel_file = "/Users/<USER>/Desktop/12-new/DR项目结构-0705.xlsx"
    
    print("=" * 60)
    print("从Excel文件生成摆位编码表")
    print("=" * 60)
    
    # 读取Excel文件
    sheets = read_excel_sheets(excel_file)
    if not sheets:
        return
    
    # 获取预定义的编码映射
    body_position_mapping, direction_mapping = get_predefined_encodings()
    
    print(f"\n使用预定义的编码映射:")
    print(f"体位编码映射: {len(body_position_mapping)} 项")
    for k, v in list(body_position_mapping.items())[:10]:
        print(f"  {k} -> {v}")
    
    print(f"\n方向编码映射: {len(direction_mapping)} 项")
    for k, v in list(direction_mapping.items())[:10]:
        print(f"  {k} -> {v}")
    
    # 使用Sheet1作为主数据
    if 'Sheet1' in sheets:
        main_data = sheets['Sheet1']
        print(f"\n使用Sheet1作为主数据源: {main_data.shape[0]} 行")
        print(f"主要列: {list(main_data.columns)}")
        
        # 生成摆位编码
        results, encoding_stats = generate_position_encoding(main_data, body_position_mapping, direction_mapping)
        
        if results:
            # 转换为DataFrame
            result_df = pd.DataFrame(results)
            
            print(f"\n生成了 {len(results)} 条摆位编码记录")
            print("\n前10条记录:")
            print(result_df.head(10).to_string(index=False))
            
            # 保存到Excel文件
            output_excel = "/Users/<USER>/Desktop/12-new/摆位编码表_从Excel生成.xlsx"
            result_df.to_excel(output_excel, index=False)
            print(f"\n✅ 已保存到Excel文件: {output_excel}")
            
            # 保存到JSON文件
            output_json = "/Users/<USER>/Desktop/12-new/摆位编码表_从Excel生成.json"
            with open(output_json, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            print(f"✅ 已保存到JSON文件: {output_json}")
            
            # 统计信息
            print(f"\n📊 统计信息:")
            print(f"总记录数: {len(results)}")
            print(f"唯一摆位数: {result_df['摆位'].nunique()}")
            print(f"唯一二级部位数: {result_df['二级部位'].nunique()}")
            print(f"唯一三级部位数: {result_df['三级部位'].nunique()}")
            print(f"唯一摆位编码数: {result_df['摆位编码'].nunique()}")
            
            # 编码分布
            print(f"\n📈 摆位编码分布:")
            encoding_counts = result_df['摆位编码'].value_counts().head(10)
            for encoding, count in encoding_counts.items():
                print(f"  {encoding}: {count} 次")
            
            # 体位和方向编码统计
            print(f"\n📊 体位编码统计:")
            for code, count in sorted(encoding_stats['body_position'].items(), key=lambda x: x[1], reverse=True):
                print(f"  {code}: {count} 次")
            
            print(f"\n📊 方向编码统计:")
            for code, count in sorted(encoding_stats['direction'].items(), key=lambda x: x[1], reverse=True):
                print(f"  {code}: {count} 次")
        
        else:
            print("\n❌ 未能生成摆位编码，请检查数据格式")
    
    else:
        print("\n❌ 未找到Sheet1，无法处理主数据")

if __name__ == "__main__":
    main()