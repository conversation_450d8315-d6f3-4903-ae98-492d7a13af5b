#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Excel输出列格式
验证检查项目清单的列顺序是否符合要求
"""

import pandas as pd
import numpy as np
from streamlit_simple import SimpleMedicalProcessor

def test_column_format():
    """测试列格式"""
    print("📋 测试Excel输出列格式")
    print("="*60)
    
    # 创建处理器
    processor = SimpleMedicalProcessor()
    
    # 加载数据
    excel_file = "NEW_检查项目名称结构表 (8).xlsx"
    success, message = processor.load_data(excel_file)
    
    if not success:
        print(f"❌ 数据加载失败：{message}")
        return
    
    print(f"✅ 数据加载成功")
    
    # 生成检查项目
    print("\n生成检查项目...")
    ct_items_raw = processor.generate_check_items('CT')
    mr_items_raw = processor.generate_check_items('MR')
    
    # 格式化输出列顺序
    ct_items = processor.format_output_columns(ct_items_raw)
    mr_items = processor.format_output_columns(mr_items_raw)
    
    print(f"✅ CT项目：{len(ct_items)}个")
    print(f"✅ MR项目：{len(mr_items)}个")
    
    # 验证列顺序
    print("\n" + "="*40)
    print("列格式验证")
    print("="*40)
    
    required_columns = [
        '模态',
        '一级编码', 
        '一级部位',
        '二级编码',
        '二级部位', 
        '三级编码',
        '三级部位',
        '项目编码',
        '项目名称'
    ]
    
    # 验证CT项目列格式
    print("\nCT项目列格式验证：")
    ct_columns = ct_items.columns.tolist()
    print(f"实际列顺序：{ct_columns}")
    print(f"要求列顺序：{required_columns}")
    
    if ct_columns == required_columns:
        print("✅ CT项目列顺序正确")
    else:
        print("❌ CT项目列顺序不正确")
        for i, (actual, required) in enumerate(zip(ct_columns, required_columns)):
            if actual != required:
                print(f"  位置{i+1}: 实际'{actual}' != 要求'{required}'")
    
    # 验证MR项目列格式
    print("\nMR项目列格式验证：")
    mr_columns = mr_items.columns.tolist()
    print(f"实际列顺序：{mr_columns}")
    print(f"要求列顺序：{required_columns}")
    
    if mr_columns == required_columns:
        print("✅ MR项目列顺序正确")
    else:
        print("❌ MR项目列顺序不正确")
        for i, (actual, required) in enumerate(zip(mr_columns, required_columns)):
            if actual != required:
                print(f"  位置{i+1}: 实际'{actual}' != 要求'{required}'")
    
    # 显示数据示例
    print("\n" + "="*40)
    print("数据示例")
    print("="*40)
    
    print("\nCT项目示例（前3条）：")
    if len(ct_items) > 0:
        for i, (_, row) in enumerate(ct_items.head(3).iterrows()):
            print(f"  {i+1}. {row['模态']} | {row['一级编码']} | {row['一级部位']} | {row['二级编码']} | {row['二级部位']} | {row['三级编码']} | {row['三级部位']} | {row['项目编码']} | {row['项目名称']}")
    
    print("\nMR项目示例（前3条）：")
    if len(mr_items) > 0:
        for i, (_, row) in enumerate(mr_items.head(3).iterrows()):
            print(f"  {i+1}. {row['模态']} | {row['一级编码']} | {row['一级部位']} | {row['二级编码']} | {row['二级部位']} | {row['三级编码']} | {row['三级部位']} | {row['项目编码']} | {row['项目名称']}")
    
    # 验证数据完整性
    print("\n" + "="*40)
    print("数据完整性验证")
    print("="*40)
    
    # 检查必填列是否有空值
    critical_columns = ['模态', '项目编码', '项目名称']
    
    print("\nCT项目数据完整性：")
    for col in critical_columns:
        empty_count = ct_items[col].isna().sum() + (ct_items[col] == '').sum()
        if empty_count == 0:
            print(f"  ✅ {col}: 无空值")
        else:
            print(f"  ❌ {col}: {empty_count}个空值")
    
    print("\nMR项目数据完整性：")
    for col in critical_columns:
        empty_count = mr_items[col].isna().sum() + (mr_items[col] == '').sum()
        if empty_count == 0:
            print(f"  ✅ {col}: 无空值")
        else:
            print(f"  ❌ {col}: {empty_count}个空值")
    
    # 验证项目编码格式
    print("\n项目编码格式验证：")
    ct_invalid_codes = ct_items[ct_items['项目编码'].str.len() != 10]
    mr_invalid_codes = mr_items[mr_items['项目编码'].str.len() != 10]
    
    if len(ct_invalid_codes) == 0:
        print("  ✅ CT项目编码格式正确（10位）")
    else:
        print(f"  ❌ CT项目编码格式错误：{len(ct_invalid_codes)}个")
    
    if len(mr_invalid_codes) == 0:
        print("  ✅ MR项目编码格式正确（10位）")
    else:
        print(f"  ❌ MR项目编码格式错误：{len(mr_invalid_codes)}个")
    
    # 测试Excel导出
    print("\n" + "="*40)
    print("Excel导出测试")
    print("="*40)
    
    try:
        from io import BytesIO
        
        buffer = BytesIO()
        with pd.ExcelWriter(buffer, engine='openpyxl') as writer:
            ct_items.to_excel(writer, sheet_name='CT检查项目清单', index=False)
            mr_items.to_excel(writer, sheet_name='MR检查项目清单', index=False)
            
            # 添加列格式说明
            format_info = pd.DataFrame({
                '列序号': [1, 2, 3, 4, 5, 6, 7, 8, 9],
                '列名称': required_columns,
                '数据类型': ['文本', '数字', '文本', '数字', '文本', '数字', '文本', '文本', '文本'],
                '说明': [
                    'CT或MR',
                    '一级部位编码',
                    '一级部位名称',
                    '二级部位编码',
                    '二级部位名称',
                    '三级部位编码',
                    '三级部位名称',
                    '10位项目编码',
                    '标准格式项目名称'
                ]
            })
            format_info.to_excel(writer, sheet_name='列格式说明', index=False)
        
        buffer.seek(0)
        
        # 验证导出的Excel文件
        test_ct = pd.read_excel(buffer, sheet_name='CT检查项目清单')
        test_mr = pd.read_excel(buffer, sheet_name='MR检查项目清单')
        test_format = pd.read_excel(buffer, sheet_name='列格式说明')
        
        print("✅ Excel导出成功")
        print(f"  CT工作表：{len(test_ct)}行，{len(test_ct.columns)}列")
        print(f"  MR工作表：{len(test_mr)}行，{len(test_mr.columns)}列")
        print(f"  格式说明工作表：{len(test_format)}行，{len(test_format.columns)}列")
        
        # 验证导出后的列顺序
        if test_ct.columns.tolist() == required_columns:
            print("  ✅ CT工作表列顺序正确")
        else:
            print("  ❌ CT工作表列顺序错误")
        
        if test_mr.columns.tolist() == required_columns:
            print("  ✅ MR工作表列顺序正确")
        else:
            print("  ❌ MR工作表列顺序错误")
        
        print(f"  格式说明工作表列：{test_format.columns.tolist()}")
        
    except Exception as e:
        print(f"❌ Excel导出测试失败：{e}")
    
    print("\n" + "="*60)
    print("🎉 列格式测试完成！")

if __name__ == "__main__":
    test_column_format()
