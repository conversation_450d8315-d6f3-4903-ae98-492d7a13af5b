#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试无部位项目生成功能
验证CT和MR无部位项目是否按照规则正确生成
"""

import pandas as pd
import numpy as np
from streamlit_simple import SimpleMedicalProcessor

def test_no_part_items():
    """测试无部位项目生成功能"""
    print("🔬 测试无部位项目生成功能")
    print("="*60)

    # 创建处理器
    processor = SimpleMedicalProcessor()

    # 加载数据
    excel_file = "NEW_检查项目名称结构表 (8).xlsx"
    success, message = processor.load_data(excel_file)

    if not success:
        print(f"❌ 数据加载失败：{message}")
        return

    print(f"✅ 数据加载成功")

    # 测试CT无部位项目生成
    print("\n" + "="*40)
    print("CT无部位项目测试")
    print("="*40)

    ct_no_part = processor.generate_no_part_items('CT')

    if ct_no_part is not None and len(ct_no_part) > 0:
        print(f"✅ 成功生成CT无部位项目：{len(ct_no_part)}个")

        # 验证数据结构
        required_columns = ['模态', '一级编码', '一级部位', '二级编码', '二级部位', '三级编码', '三级部位', '项目编码', '项目名称']
        missing_columns = [col for col in required_columns if col not in ct_no_part.columns]

        if missing_columns:
            print(f"❌ 缺少必需列：{missing_columns}")
        else:
            print("✅ 数据结构正确，包含所有必需列")

        # 验证生成规则
        print("\n验证CT无部位项目生成规则：")

        # 检查一级部位
        unique_level1 = ct_no_part['一级部位'].unique()
        if len(unique_level1) == 1 and unique_level1[0] == '无指定部位':
            print("  ✅ 一级部位 = '无指定部位'")
        else:
            print(f"  ❌ 一级部位错误：{unique_level1}")

        # 检查一级编码
        unique_level1_code = ct_no_part['一级编码'].unique()
        if len(unique_level1_code) == 1 and unique_level1_code[0] == '09':
            print("  ✅ 一级编码 = '09'")
        else:
            print(f"  ❌ 一级编码错误：{unique_level1_code}")

        # 检查模态
        unique_modality = ct_no_part['模态'].unique()
        if len(unique_modality) == 1 and unique_modality[0] == 'CT':
            print("  ✅ 模态 = 'CT'")
        else:
            print(f"  ❌ 模态错误：{unique_modality}")

        # 检查项目编码格式（允许字母编码）
        invalid_codes = ct_no_part[~ct_no_part['项目编码'].str.match(r'^CT09[0-9a-f]{4}00$')]
        if len(invalid_codes) == 0:
            print("  ✅ 项目编码格式正确：CT09[二级编码][三级编码]00")
        else:
            print(f"  ❌ 项目编码格式错误：{len(invalid_codes)}个")
            for _, row in invalid_codes.head(3).iterrows():
                print(f"    {row['项目编码']} - {row['项目名称']}")

        # 显示示例
        print(f"\nCT无部位项目示例（前5个）：")
        for i, (_, row) in enumerate(ct_no_part.head(5).iterrows()):
            print(f"  {i+1}. {row['项目编码']} - {row['项目名称']}")
            print(f"     二级部位: {row['二级部位']} | 三级部位: {row['三级部位']}")
    else:
        print("❌ 未生成CT无部位项目")

    # 测试MR无部位项目生成
    print("\n" + "="*40)
    print("MR无部位项目测试")
    print("="*40)

    mr_no_part = processor.generate_no_part_items('MR')

    if mr_no_part is not None and len(mr_no_part) > 0:
        print(f"✅ 成功生成MR无部位项目：{len(mr_no_part)}个")

        # 验证数据结构
        missing_columns = [col for col in required_columns if col not in mr_no_part.columns]

        if missing_columns:
            print(f"❌ 缺少必需列：{missing_columns}")
        else:
            print("✅ 数据结构正确，包含所有必需列")

        # 验证生成规则
        print("\n验证MR无部位项目生成规则：")

        # 检查一级部位
        unique_level1 = mr_no_part['一级部位'].unique()
        if len(unique_level1) == 1 and unique_level1[0] == '无指定部位':
            print("  ✅ 一级部位 = '无指定部位'")
        else:
            print(f"  ❌ 一级部位错误：{unique_level1}")

        # 检查一级编码
        unique_level1_code = mr_no_part['一级编码'].unique()
        if len(unique_level1_code) == 1 and unique_level1_code[0] == '09':
            print("  ✅ 一级编码 = '09'")
        else:
            print(f"  ❌ 一级编码错误：{unique_level1_code}")

        # 检查模态
        unique_modality = mr_no_part['模态'].unique()
        if len(unique_modality) == 1 and unique_modality[0] == 'MR':
            print("  ✅ 模态 = 'MR'")
        else:
            print(f"  ❌ 模态错误：{unique_modality}")

        # 检查项目编码格式（允许字母编码）
        invalid_codes = mr_no_part[~mr_no_part['项目编码'].str.match(r'^MR09[0-9a-f]{4}00$')]
        if len(invalid_codes) == 0:
            print("  ✅ 项目编码格式正确：MR09[二级编码][三级编码]00")
        else:
            print(f"  ❌ 项目编码格式错误：{len(invalid_codes)}个")
            for _, row in invalid_codes.head(3).iterrows():
                print(f"    {row['项目编码']} - {row['项目名称']}")

        # 显示示例
        print(f"\nMR无部位项目示例（前5个）：")
        for i, (_, row) in enumerate(mr_no_part.head(5).iterrows()):
            print(f"  {i+1}. {row['项目编码']} - {row['项目名称']}")
            print(f"     二级部位: {row['二级部位']} | 三级部位: {row['三级部位']}")
    else:
        print("❌ 未生成MR无部位项目")

    # 测试完整项目生成（包含无部位项目）
    print("\n" + "="*40)
    print("完整项目生成测试")
    print("="*40)

    ct_items_raw = processor.generate_check_items('CT')
    mr_items_raw = processor.generate_check_items('MR')

    ct_items = processor.format_output_columns(ct_items_raw)
    mr_items = processor.format_output_columns(mr_items_raw)

    if ct_items is not None and mr_items is not None:
        # 统计无部位项目
        ct_no_part_count = len(ct_items[ct_items['一级部位'] == '无指定部位'])
        mr_no_part_count = len(mr_items[mr_items['一级部位'] == '无指定部位'])
        ct_with_part_count = len(ct_items) - ct_no_part_count
        mr_with_part_count = len(mr_items) - mr_no_part_count

        print(f"✅ 完整项目生成成功")
        print(f"  CT项目总数：{len(ct_items)} (有部位: {ct_with_part_count}, 无部位: {ct_no_part_count})")
        print(f"  MR项目总数：{len(mr_items)} (有部位: {mr_with_part_count}, 无部位: {mr_no_part_count})")
        print(f"  总项目数：{len(ct_items) + len(mr_items)}")

        # 验证无部位项目是否正确合并
        if ct_no_part_count > 0:
            print(f"\n✅ CT无部位项目已正确合并到CT检查项目清单中")
        if mr_no_part_count > 0:
            print(f"✅ MR无部位项目已正确合并到MR检查项目清单中")

        # 显示混合示例（有部位和无部位项目）
        print(f"\nCT项目混合示例：")
        ct_with_part = ct_items[ct_items['一级部位'] != '无指定部位'].head(2)
        ct_no_part_sample = ct_items[ct_items['一级部位'] == '无指定部位'].head(2)

        for i, (_, row) in enumerate(ct_with_part.iterrows()):
            print(f"  [有部位] {row['项目编码']} - {row['项目名称']}")
        for i, (_, row) in enumerate(ct_no_part_sample.iterrows()):
            print(f"  [无部位] {row['项目编码']} - {row['项目名称']}")

        print(f"\nMR项目混合示例：")
        mr_with_part = mr_items[mr_items['一级部位'] != '无指定部位'].head(2)
        mr_no_part_sample = mr_items[mr_items['一级部位'] == '无指定部位'].head(2)

        for i, (_, row) in enumerate(mr_with_part.iterrows()):
            print(f"  [有部位] {row['项目编码']} - {row['项目名称']}")
        for i, (_, row) in enumerate(mr_no_part_sample.iterrows()):
            print(f"  [无部位] {row['项目编码']} - {row['项目名称']}")
    else:
        print("❌ 完整项目生成失败")

    # 验证编码唯一性
    print("\n" + "="*40)
    print("编码唯一性验证")
    print("="*40)

    if ct_items is not None and mr_items is not None:
        all_codes = list(ct_items['项目编码']) + list(mr_items['项目编码'])
        unique_codes = set(all_codes)

        if len(all_codes) == len(unique_codes):
            print("✅ 所有项目编码唯一，无重复")
        else:
            duplicate_count = len(all_codes) - len(unique_codes)
            print(f"❌ 发现重复编码：{duplicate_count}个")

            # 找出重复的编码
            code_counts = pd.Series(all_codes).value_counts()
            duplicates = code_counts[code_counts > 1]

            print("重复编码详情：")
            for code, count in duplicates.head(5).items():
                print(f"  {code}: 重复{count}次")

    print("\n" + "="*60)
    print("🎉 无部位项目功能测试完成！")

if __name__ == "__main__":
    test_no_part_items()
